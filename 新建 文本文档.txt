*** System restart required ***
Last login: <PERSON><PERSON> Aug 19 19:14:02 2025 from 172.16.0.106
root@BEST-YCWL:~# docker ps | grep mysql
02b8f3d29be9   mysql:5.7.25                                    "docker-entrypoint.s…"   2 weeks ago     Up 2 weeks   0.0.0.0:3306->3306/tcp, :::3306->3306/tcp, 33060/tcp   mysql
root@BEST-YCWL:~# docker ps | grep mysql
02b8f3d29be9   mysql:5.7.25                                    "docker-entrypoint.s…"   2 weeks ago     Up 2 weeks   0.0.0.0:3306->3306/tcp, :::3306->3306/tcp, 33060/tcp   mysql
root@BEST-YCWL:~# docker exec -it mysql bash
root@02b8f3d29be9:/# mysql -u root -p
Enter password:
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
root@02b8f3d29be9:/# mysql -u root -p
Enter password:
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
root@02b8f3d29be9:/# mysql -u root -p
Enter password:
ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
root@02b8f3d29be9:/# mysql -u root -p
Enter password:
Welcome to the MySQL monitor.  Commands end with ; or \g.
Your MySQL connection id is 420
Server version: 5.7.25 MySQL Community Server (GPL)

Copyright (c) 2000, 2019, Oracle and/or its affiliates. All rights reserved.

Oracle is a registered trademark of Oracle Corporation and/or its
affiliates. Other names may be trademarks of their respective
owners.

Type 'help;' or '\h' for help. Type '\c' to clear the current input statement.

mysql> SHOW DATABASES;
+--------------------+
| Database           |
+--------------------+
| information_schema |
| mysql              |
| performance_schema |
| sys                |
| ycwl               |
| ycwl_slave1        |
| ycwl_slave2        |
| ycwl_slave3        |
+--------------------+
8 <USER> <GROUP> set (0.00 sec)

mysql> SHOW DATABASES;USE ycdb;
+--------------------+
| Database           |
+--------------------+
| information_schema |
| mysql              |
| performance_schema |
| sys                |
| ycwl               |
| ycwl_slave1        |
| ycwl_slave2        |
| ycwl_slave3        |
+--------------------+
8 <USER> <GROUP> set (0.00 sec)

ERROR 1049 (42000): Unknown database 'ycdb'
mysql> SHOW TABLES;EXIT;
ERROR 1046 (3D000): No database selected
Bye
root@02b8f3d29be9:/# USE ycdb;
bash: USE: command not found
root@02b8f3d29be9:/# USE ycdb;
bash: USE: command not found
root@02b8f3d29be9:/# SHOW TABLES;
bash: SHOW: command not found
root@02b8f3d29be9:/# mysql -u root -p
Enter password:
Welcome to the MySQL monitor.  Commands end with ; or \g.
Your MySQL connection id is 421
Server version: 5.7.25 MySQL Community Server (GPL)

Copyright (c) 2000, 2019, Oracle and/or its affiliates. All rights reserved.

Oracle is a registered trademark of Oracle Corporation and/or its
affiliates. Other names may be trademarks of their respective
owners.

Type 'help;' or '\h' for help. Type '\c' to clear the current input statement.

mysql> USE ycdb;
ERROR 1049 (42000): Unknown database 'ycdb'
mysql> USE ycwl;
Reading table information for completion of table and column names
You can turn off this feature to get a quicker startup with -A

Database changed
mysql> SHOW TABLES;
+-------------------------+
| Tables_in_ycwl          |
+-------------------------+
| accumulation            |
| accumulation_back       |
| area                    |
| car                     |
| car_back                |
| centerdistance          |
| delivery_area           |
| delivery_type           |
| dist                    |
| error_point             |
| error_point_back        |
| feedback                |
| feedback_file           |
| feedback_reply          |
| feedback_reply_file     |
| file_import_logs        |
| gear                    |
| group                   |
| group_areas             |
| operation               |
| pickup_user             |
| pickup_user_import      |
| pickup_user_parameter   |
| point_distance          |
| role                    |
| role_operation          |
| route                   |
| route_accumulation      |
| route_back              |
| route_copy_522          |
| route_detail            |
| route_user              |
| scheduling              |
| scheduling_user         |
| second_transit          |
| shaoguan_boundary_point |
| site_selection          |
| site_store              |
| store                   |
| store_back1             |
| store_back2             |
| store_time              |
| store_two               |
| system_parameter        |
| team                    |
| transit_delivery        |
| transit_depot           |
| transit_depot_back      |
| transit_depot_car       |
| travel_time             |
| unloading_time          |
| user                    |
| user_group              |
| version                 |
+-------------------------+
54 <USER> <GROUP> set (0.00 sec)

mysql> EXIT;
Bye
root@02b8f3d29be9:/# exit
exit
root@BEST-YCWL:~#