server:
  port: 8083
spring:
  application:
    name: clustercalculate
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
  mvc:
    servlet:
      load-on-startup: 1
  # ===== 数据库配置 =====
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: **************************************************************************************************************************
        username: root
        password: 123456
        driver-class-name: com.mysql.jdbc.Driver
      slave1:
        url: *********************************************************************************************************************************
        username: root
        password: 123456
        driver-class-name: com.mysql.jdbc.Driver
      slave2:
        url: *********************************************************************************************************************************
        username: root
        password: 123456
        driver-class-name: com.mysql.jdbc.Driver
      slave3:
        url: *********************************************************************************************************************************
        username: root
        password: 123456
        driver-class-name: com.mysql.jdbc.Driver

mybatis:
  type-aliases-package: com.ict.ycwl.clustercalculate.pojo
  configuration:
    map-underscore-to-camel-case: true
logging:
  level:
    cn.itcast: debug
knife4j:
  enable: false

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ict.ycwl.clustercalculate.pojo
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# ===== 自定义配置 =====
jjking:
  dbPath: /www/wwwroot/ycwl/masterDatasource.txt